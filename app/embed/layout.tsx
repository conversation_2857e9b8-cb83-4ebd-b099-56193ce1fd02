import { Toaster } from 'sonner';
import type { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>st_Mono } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';
import { TooltipProvider } from '@/components/ui/tooltip';

import '@/app/globals.css';

export const metadata: Metadata = {
  title: 'Chat SDK - Embed Mode',
  description: 'AI Chatbot Template for Embedding',
  robots: 'noindex, nofollow', // Prevent indexing of embed pages
};

const geist = Geist({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist',
});

const geistMono = Geist_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-geist-mono',
});

export default function EmbedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={`${geist.variable} ${geistMono.variable}`}
    >
      <head />
      <body className="antialiased h-screen overflow-hidden" suppressHydrationWarning>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          <TooltipProvider>
            <Toaster position="top-center" />
            {children}
          </TooltipProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
