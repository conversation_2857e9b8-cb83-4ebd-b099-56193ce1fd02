<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat SDK iframe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
        }
        iframe {
            border: 1px solid #ccc;
            border-radius: 8px;
            width: 100%;
        }
        .config-panel {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        input, select {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }

        .info {
            background-color: #e7f3ff;
            padding: 10px;
            border-left: 4px solid #007cba;
            margin: 10px 0;
        }
        .debug-log {
            background-color: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>🤖 Chat SDK iframe Integration Test</h1>

    <div class="info">
        <p><strong>Purpose:</strong> Test Chat SDK iframe integration across different domains</p>
        <p><strong>Allowed Origins:</strong> iframetester.com, edu3.dotb.cloud, *.dotb.cloud, localhost, ngrok tunnels</p>
    </div>

    <!-- Configuration Panel -->
    <div class="test-section">
        <h2>⚙️ Configuration</h2>
        <div class="config-panel">
            <label for="baseUrl">Chat SDK Base URL:</label>
            <select id="baseUrl">
                <option value="http://localhost:3001">Local Development (localhost:3001)</option>
                <option value="https://cs-agent.vercel.app">Production (cs-agent.vercel.app)</option>
                <option value="https://your-ngrok-url.ngrok-free.app">Ngrok Tunnel (Update URL)</option>
            </select>

            <label for="customUrl">Or Custom URL:</label>
            <input type="text" id="customUrl" placeholder="https://your-custom-url.com" />

            <label for="userId">User ID:</label>
            <input type="text" id="userId" value="test-user-123" placeholder="Enter user ID">

            <label for="source">Source:</label>
            <input type="text" id="source" value="iframe-tester" placeholder="Enter source identifier">

            <button onclick="updateIframes()">🔄 Update iframes</button>
            <button onclick="clearDebugLog()">🧹 Clear Debug Log</button>
        </div>

        <div class="debug-log" id="debugLog">
            <div>🚀 Debug Log - Ready for testing...</div>
        </div>
    </div>

    <!-- Simple Parameters Authentication Test -->
    <div class="test-section">
        <h2>🔑 Simple Parameters Authentication Test</h2>
        <p>This iframe uses simple user_id and source parameters (less secure, for testing).</p>
        <iframe id="simpleIframe" height="600" src="about:blank"></iframe>
    </div>

    <!-- PostMessage Communication Test -->
    <div class="test-section">
        <h2>📨 PostMessage Communication Test</h2>
        <p>Test communication between parent window and iframe.</p>
        <button onclick="sendMessageToIframe()">📤 Send Message to iframe</button>
        <button onclick="requestIframeStatus()">📊 Request iframe Status</button>
        <div id="messageLog" class="debug-log" style="max-height: 200px;">
            <div>📨 PostMessage Log - Ready...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const debugLog = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';

            const logEntry = document.createElement('div');
            logEntry.className = className;
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        function clearDebugLog() {
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML = '<div>🚀 Debug Log - Cleared...</div>';
        }

        function getBaseUrl() {
            const customUrl = document.getElementById('customUrl').value.trim();
            if (customUrl) {
                log(`Using custom URL: ${customUrl}`);
                return customUrl;
            }

            const baseUrl = document.getElementById('baseUrl').value;
            log(`Using selected URL: ${baseUrl}`);
            return baseUrl;
        }



        function updateIframes() {
            const baseUrl = getBaseUrl();
            const userId = document.getElementById('userId').value;
            const source = document.getElementById('source').value;

            log('🔄 Updating iframes...');

            // Update simple iframe
            if (userId && source) {
                const simpleUrl = `${baseUrl}/embed?user_id=${encodeURIComponent(userId)}&source=${encodeURIComponent(source)}`;
                document.getElementById('simpleIframe').src = simpleUrl;
                log(`Simple iframe URL: ${simpleUrl}`);
            } else {
                log('User ID and Source required for simple iframe', 'warning');
            }

            log('✅ iframe updated', 'success');
        }

        function sendMessageToIframe() {
            const message = {
                type: 'test-message',
                data: { hello: 'from parent window', timestamp: Date.now() }
            };

            const simpleIframe = document.getElementById('simpleIframe');

            if (simpleIframe.contentWindow) {
                simpleIframe.contentWindow.postMessage(message, '*');
                logMessage('📤 Sent message to Simple iframe');
            }
        }

        function requestIframeStatus() {
            const message = { type: 'status-request' };

            const simpleIframe = document.getElementById('simpleIframe');

            if (simpleIframe.contentWindow) {
                simpleIframe.contentWindow.postMessage(message, '*');
                logMessage('📊 Requested status from Simple iframe');
            }
        }

        function logMessage(message) {
            const messageLog = document.getElementById('messageLog');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            messageLog.appendChild(logEntry);
            messageLog.scrollTop = messageLog.scrollHeight;
        }

        // Listen for messages from iframes
        window.addEventListener('message', function(event) {
            logMessage(`📥 Received: ${JSON.stringify(event.data)} from ${event.origin}`);
        });

        // Monitor iframe load events
        document.getElementById('simpleIframe').addEventListener('load', function() {
            log('Simple iframe loaded', 'success');
        });

        document.getElementById('simpleIframe').addEventListener('error', function() {
            log('Simple iframe failed to load', 'error');
        });

        // Initialize
        log('🚀 iframe Test Page Initialized');
        log('💡 Tip: Enter User ID and Source, then update iframe to test authentication');
    </script>
</body>
</html>
