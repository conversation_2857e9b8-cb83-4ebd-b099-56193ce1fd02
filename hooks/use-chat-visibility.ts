'use client';

import { useMemo, useEffect, useState } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { unstable_serialize } from 'swr/infinite';
import { updateChatVisibility } from '@/app/(chat)/actions';
import {
  getChatHistoryPaginationKey,
  type ChatHistory,
} from '@/components/sidebar-history';
import type { VisibilityType } from '@/components/visibility-selector';

export function useChatVisibility({
  chatId,
  initialVisibilityType,
}: {
  chatId: string;
  initialVisibilityType: VisibilityType;
}) {
  const { mutate, cache } = useSWRConfig();
  const [visibilityType, setVisibilityTypeState] = useState<VisibilityType>(initialVisibilityType);

  const { data: localVisibility, mutate: setLocalVisibility } = useSWR(
    `${chatId}-visibility`,
    null,
    {
      fallbackData: initialVisibilityType,
    },
  );

  // Use useEffect to update visibility type when history changes
  useEffect(() => {
    const history: ChatHistory = cache.get('/api/history')?.data;

    if (!history) {
      setVisibilityTypeState(localVisibility || initialVisibilityType);
      return;
    }

    const chat = history.chats.find((chat) => chat.id === chatId);
    if (!chat) {
      setVisibilityTypeState('private');
      return;
    }

    setVisibilityTypeState(chat.visibility);
  }, [cache, chatId, localVisibility, initialVisibilityType]);

  const setVisibilityType = (updatedVisibilityType: VisibilityType) => {
    setVisibilityTypeState(updatedVisibilityType);
    setLocalVisibility(updatedVisibilityType);
    mutate(unstable_serialize(getChatHistoryPaginationKey));

    updateChatVisibility({
      chatId: chatId,
      visibility: updatedVisibilityType,
    });
  };

  return { visibilityType, setVisibilityType };
}
