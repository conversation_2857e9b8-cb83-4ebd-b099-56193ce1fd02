# Generate a random secret: https://generate-secret.vercel.app/32 or `openssl rand -base64 32`
AUTH_SECRET=****

# The following keys below are automatically created and
# added to your environment when you deploy on vercel

# Get your Google Generative AI API Key here: https://aistudio.google.com/app/apikey
GOOGLE_GENERATIVE_AI_API_KEY=****

# Get your xAI API Key here for chat and image models: https://console.x.ai/
XAI_API_KEY=****

# Get your Groq API Key here: https://console.groq.com/keys
GROQ_API_KEY=****

# Google Cloud Configuration for Caching
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# Cache Configuration
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=86400  # 24 hours in seconds
CACHE_MAX_SIZE=10485760  # 10MB

# PathRAG API Configuration
PATHRAG_API_URL=http://localhost:8000

# Instructions to create a Vercel Blob Store here: https://vercel.com/docs/storage/vercel-blob
BLOB_READ_WRITE_TOKEN=****

# Instructions to create a PostgreSQL database here: https://vercel.com/docs/storage/vercel-postgres/quickstart
POSTGRES_URL=****
