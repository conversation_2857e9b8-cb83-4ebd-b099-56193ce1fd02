import { NextResponse, type NextRequest } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { guestRegex, isDevelopmentEnvironment } from './lib/constants';

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  /*
   * <PERSON><PERSON> starts the dev server and requires a 200 status to
   * begin the tests, so this ensures that the tests can start
   */
  if (pathname.startsWith('/ping')) {
    return new Response('pong', { status: 200 });
  }

  // Handle embed routes with special authentication
  if (pathname.startsWith('/embed')) {
    // Allow auth-error page without authentication
    if (pathname.startsWith('/embed/auth-error')) {
      return NextResponse.next();
    }

    // Log request info for debugging
    const referer = request.headers.get('referer');
    const origin = request.headers.get('origin');
    console.log('Embed request:', { pathname, referer, origin, searchParams: Object.fromEntries(searchParams) });

    // Check for simple user_id + source authentication
    const userId = searchParams.get('user_id');
    const source = searchParams.get('source');

    // Simple user_id + source validation
    if (userId && source) {
      console.log('Using simple auth:', { userId, source });
      return NextResponse.next();
    }

    // No valid authentication, redirect to error
    console.log('No valid authentication provided');
    return NextResponse.redirect(
      new URL('/embed/auth-error?error=missing_auth', request.url)
    );
  }

  // Skip middleware for iframe API routes
  if (pathname.startsWith('/api/iframe')) {
    return NextResponse.next();
  }

  // Skip middleware for embed API routes
  if (pathname.startsWith('/api/embed')) {
    return NextResponse.next();
  }

  if (pathname.startsWith('/api/auth')) {
    return NextResponse.next();
  }

  const token = await getToken({
    req: request,
    secret: process.env.AUTH_SECRET,
    secureCookie: !isDevelopmentEnvironment,
  });

  if (!token) {
    const redirectUrl = encodeURIComponent(request.url);

    return NextResponse.redirect(
      new URL(`/api/auth/guest?redirectUrl=${redirectUrl}`, request.url),
    );
  }

  const isGuest = guestRegex.test(token?.email ?? '');

  if (token && !isGuest && ['/login', '/register'].includes(pathname)) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Root path
    '/',
    // Chat paths
    '/chat/:path*',
    // API paths
    '/api/:path*',
    // Auth paths
    '/login',
    '/register',
  ],
};
