'use client';

import { EmbedChat } from './embed-chat';
import type { UIMessage } from 'ai';
import type { Session } from 'next-auth';

export function EmbedChatWrapper({
  id,
  session,
  initialMessages = [],
  initialChatModel = 'chat-model',
  initialVisibilityType = 'private',
  isReadonly = false,
  autoResume = false,
}: {
  id: string;
  session: Session;
  initialMessages?: Array<UIMessage>;
  initialChatModel?: string;
  initialVisibilityType?: 'private' | 'public';
  isReadonly?: boolean;
  autoResume?: boolean;
}) {
  return (
    <EmbedChat
      id={id}
      initialMessages={initialMessages}
      initialChatModel={initialChatModel}
      initialVisibilityType={initialVisibilityType}
      isReadonly={isReadonly}
      session={session}
      autoResume={autoResume}
    />
  );
}