# Project Intelligence for Chat SDK

This file captures important patterns, preferences, and project intelligence to help <PERSON> work more effectively on the Chat SDK project.

## Key Patterns & Preferences

- **Language**: Vietnamese for user interaction, English for code comments unless specified.
- **Framework**: Next.js 15 with App Router is the primary framework.
- **Styling**: Tailwind CSS with Shadcn/UI components.
- **State Management**: React Context API for global state, local state for component-specific data.
- **AI Integration**: Vercel AI SDK v4.2+, primarily Google Gemini models with custom middleware.
- **Database**: PostgreSQL with Drizzle ORM.
- **Testing**: Playwright for E2E tests.
- **Linting/Formatting**: ESLint with Next.js core web vitals, <PERSON><PERSON>er, Tailwind CSS plugin.

## Critical Implementation Paths

- **Authentication**: NextAuth.js v5 (beta) is used. Pay attention to guest access and authenticated user flows.
- **AI SDK Middleware**: Custom middleware is used for AI provider interactions, including caching and reasoning extraction.
- **Streaming Responses**: Ensure all AI responses are streamed using data stream protocol for better UX.
- **Multimodal Input**: Handle text and file uploads (images, documents) with proper type safety.

## User Workflow Preferences

- **Memory Bank First**: Always read and understand the Memory Bank files at the start of any task.
- **Plan then Act**: Present a plan (PLAN mode) and wait for approval before implementing (ACT mode).
- **Clear Communication**: Prefix responses with mode indicators and file review sections.

## Known Challenges

- **Google Gen AI SDK Integration**: Potential TypeScript compatibility issues or API parameter complexities, especially with the new caching system.
- **Next.js 15 & React 19 RC**: Being on release candidate versions might introduce unexpected behavior or require workarounds.

## Next.js 15 App Router Best Practices

### File Structure Conventions
- **Top-level folders**: `app/`, `components/`, `lib/`, `public/`, `src/` (optional)
- **App Router structure**: Use `app/` directory for all routing
- **Route grouping**: Use parentheses for logical grouping: `(auth)`, `(chat)` - these don't affect URL structure
- **Dynamic routes**: Use square brackets: `[id]`, `[...slug]` for catch-all routes
- **Private folders**: Prefix with underscore `_folder` to exclude from routing
- **Colocation**: Components can be colocated within route segments safely

### Special Files in App Router
- `layout.tsx`: Shared UI for route segments
- `page.tsx`: Unique UI for routes
- `loading.tsx`: Loading UI with React Suspense
- `error.tsx`: Error UI with React Error Boundary
- `not-found.tsx`: 404 UI
- `route.ts`: API endpoints
- `middleware.ts`: Request middleware (root level only)

### Component Organization
- **Feature-based grouping**: Organize components by feature/module
- **Separation of concerns**: Distinguish between presentational and container components
- **Shared components**: Place in `components/` directory with clear naming
- **Component structure**: Each component folder can contain:
  - `index.tsx` (main component)
  - `types.ts` (component-specific types)
  - `utils.ts` (component-specific utilities)

## TypeScript Best Practices

### Type Safety
- **Strict mode**: Always use TypeScript strict mode
- **No implicit any**: Avoid `any` types, use proper type definitions
- **Interface over type**: Prefer `interface` for object shapes, `type` for unions/intersections
- **Generic constraints**: Use proper generic constraints for reusable components
- **Utility types**: Leverage TypeScript utility types (`Pick`, `Omit`, `Partial`, etc.)

### Next.js Specific Types
- Use `NextRequest`, `NextResponse` for API routes
- Import types from `next/app` for App Router specific types
- Use `Metadata` type for SEO metadata
- Proper typing for `params` and `searchParams` in page components

### AI SDK Type Patterns
- Import types from `ai` package: `LanguageModelV1Middleware`, `LanguageModelV1StreamPart`
- Use proper typing for streaming responses and tool calls
- Type middleware functions correctly for custom AI integrations

## AI SDK Integration Best Practices

### Streaming Implementation
- **Always use streaming**: Implement streaming for all AI responses using `streamText`
- **Data stream protocol**: Use data streams over text streams for rich content (tool calls, reasoning, sources)
- **Error handling**: Implement proper error handling with `onError` callbacks
- **Stream protocol**: Set `x-vercel-ai-data-stream: v1` header for custom backends

### Middleware Usage
- **Language model middleware**: Use `wrapLanguageModel` for enhancing model behavior
- **Built-in middleware**: Leverage `extractReasoningMiddleware`, `simulateStreamingMiddleware`, `defaultSettingsMiddleware`
- **Multiple middleware**: Apply multiple middlewares in correct order
- **Custom middleware**: Implement `transformParams`, `wrapGenerate`, `wrapStream` for custom behavior

### Provider Integration
- **Provider flexibility**: Use unified provider API for easy model switching
- **Custom providers**: Use `customProvider` for non-standard AI services
- **Environment variables**: Always check for API keys before initializing providers
- **Error handling**: Implement proper error handling for provider failures

## Database & API Patterns

### Error Handling
- **Custom errors**: Use `ChatSDKError` for all database and API errors
- **Proper error types**: Define specific error types for different failure scenarios
- **Error boundaries**: Implement React error boundaries for UI error handling
- **Graceful degradation**: Provide fallback UI for error states

### Database Queries
- **Drizzle ORM**: Use Drizzle ORM patterns consistently
- **Type safety**: Ensure all database queries are properly typed
- **Error wrapping**: Wrap all database operations in try-catch with `ChatSDKError`
- **Connection management**: Proper database connection handling

## Authentication Patterns

### NextAuth.js v5 Beta
- **Session management**: Proper session handling with type extensions
- **Guest access**: Implement guest user flows correctly
- **Middleware integration**: Proper integration with Next.js middleware
- **Type extensions**: Extend NextAuth types for custom user properties

## Performance & Optimization

### Next.js Optimizations
- **Server Components**: Use React Server Components by default
- **Client Components**: Mark with `'use client'` only when necessary
- **Dynamic imports**: Use dynamic imports for code splitting
- **Image optimization**: Use Next.js Image component for optimized images
- **Font optimization**: Use Next.js font optimization features

### Caching Strategies
- **Google Cached Content**: Implement caching for AI responses
- **Next.js caching**: Leverage Next.js built-in caching mechanisms
- **Database caching**: Implement proper database query caching

## Tool Usage Patterns

- Prioritize `search_by_regex` for locating code before `view_files`.
- Use `update_file` or `edit_file_fast_apply` for modifications, choosing the most cost-effective.
- Confirm file existence before writing or editing.
- Use `search_codebase` for semantic searches when exact patterns are unknown.

## Project Evolution & Decisions

- **HITL Removal**: Human-in-the-Loop system was removed to favor fully autonomous operation.
- **Knowledge Retrieval**: `getPathRAGKnowledge` is the sole tool for knowledge base access, replacing a previous `get-knowledge` tool.
- **Caching System**: A new Google Cached Content system is under active development to optimize cost and performance.
- **AI SDK v4.2+**: Using latest AI SDK features including reasoning models, MCP clients, and message parts.

## Common Pitfalls to Avoid

### Next.js 15 Specific
- **React 19 RC**: Be aware of potential breaking changes in React 19 RC
- **App Router migration**: Ensure proper migration from Pages Router patterns
- **Metadata API**: Use new Metadata API instead of Head component
- **Route handlers**: Use new route handler patterns instead of API routes

### TypeScript Issues
- **Type imports**: Use `import type` for type-only imports
- **Module resolution**: Ensure proper module resolution in `tsconfig.json`
- **Declaration files**: Don't modify `next-env.d.ts`, create separate declaration files

### AI SDK Streaming
- **Stream interruption**: Handle stream interruptions gracefully
- **Memory leaks**: Properly cleanup streaming connections
- **Error propagation**: Ensure errors in streams are properly propagated to UI

### Database Operations
- **Connection pooling**: Proper database connection management
- **Transaction handling**: Use database transactions for complex operations
- **Query optimization**: Optimize database queries for performance

### Environment & Configuration
- **Environment variables**: Always validate required environment variables
- **Configuration validation**: Validate configuration at startup
- **Secret management**: Never commit secrets to repository

## Code Quality Standards

### Linting & Formatting
- **ESLint configuration**: Follow project ESLint rules (Next.js core web vitals, import rules, Tailwind)
- **Prettier integration**: Use Prettier for consistent code formatting
- **Import organization**: Organize imports with proper grouping and sorting

### Testing Patterns
- **Playwright E2E**: Use Playwright for end-to-end testing
- **Component testing**: Test components in isolation
- **API testing**: Test API routes thoroughly
- **Error scenarios**: Test error handling scenarios

### Security Best Practices
- **Input validation**: Validate all user inputs
- **CSRF protection**: Implement CSRF protection for forms
- **Rate limiting**: Implement rate limiting for API endpoints
- **Content Security Policy**: Implement proper CSP headers

## Future Considerations

- Expansion of caching to knowledge base responses and document content.
- Performance analytics for cache hit rates and cost savings.

---
*This file is a living document. Update it as new patterns and insights emerge.*

## Cursor Rules

# Project Overview: Chat SDK

This project, **Chat SDK**, is an autonomous AI chatbot template designed for production use. It's built to be a versatile platform for developing intelligent, automated chatbot applications for businesses.

**Core Mission:**
- Provide a production-ready AI chatbot template.
- Support integration with multiple AI models (e.g., Google Gemini, xAI Grok).
- Include flexible authentication (guest access + authenticated users).
- Integrate with a knowledge base for business-specific queries.
- Enable autonomous operation without human intervention.
- Support file uploads and multimodal interactions.

For more details on the project's goals and target use cases, see [.memory-bank/projectbrief.md](mdc:.memory-bank/projectbrief.md).

## Core Technologies

The Chat SDK leverages a modern technology stack:

- **Framework & Runtime:** [Next.js 15](mdc:next.config.ts) (App Router with React Server Components), [TypeScript](mdc:tsconfig.json) (strict mode), React 19 RC, Node.js.
- **AI & LLM Integration:** [AI SDK by Vercel](mdc:package.json) (v4.3.13), Google Gemini (primary models: `gemini-2.5-flash-preview-04-17`, `gemini-2.0-flash-lite`), Google Gen AI SDK (`@google/genai` v1.1.0), xAI Grok, OpenRouter.
- **Database & Storage:** PostgreSQL (via [Vercel Postgres](mdc:drizzle.config.ts)), [Drizzle ORM](mdc:drizzle.config.ts) (v0.34.0), Vercel Blob for file storage.
- **Authentication:** [NextAuth 5.0 Beta](mdc:app/(auth)/auth.ts).
- **UI & Styling:** Shadcn/UI, Tailwind CSS.
- **Development Tools:** Biome, ESLint, Playwright.
- **Package Manager:** [pnpm](mdc:pnpm-lock.yaml).

For a comprehensive list of technologies and their configurations, refer to [.memory-bank/techContext.md](mdc:.memory-bank/techContext.md).

# Application Structure

## Full Codebase Structure

```text
.
├── .github/
│   └── workflows/
├── .memory-bank/
│   ├── activeContext.md
│   ├── productContext.md
│   ├── progress.md
│   ├── projectbrief.md
│   ├── systemPatterns.md
│   └── techContext.md
├── .cursor/
│   └── rules/
│       ├── 01_project_overview.mdc
│       ├── 02_application_structure.mdc
│       ├── 03_ai_system.mdc
│       ├── 04_database_schema.mdc
│       ├── 05_authentication.mdc
│       └── 06_pathrag_integration.mdc
├── .next/
│   ├── cache/
│   ├── diagnostics/
│   ├── server/
│   ├── chunks/
│   ├── edge/
│   ├── instrumentation/
│   ├── middleware/
│   └── pages/
├── .trae/
│   └── rules/
├── app/
│   ├── (auth)/
│   ├── (chat)/
│   ├── api/
│   ├── debug/
│   └── embed/
├── components/
│   └── ui/
├── hooks/
├── lib/
│   ├── ai/
│   │   ├── providers.ts
│   │   ├── models.ts
│   │   ├── prompts.ts
│   │   ├── middleware/
│   │   └── tools/
│   ├── db/
│   │   ├── schema.ts
│   │   ├── queries.ts
│   │   └── migrations/
│   └── editor/
├── pathrag/
│   └── knowledgebase/
├── public/
│   └── images/
├── scripts/
├── tests/
│   └── prompts/
├── .clauderules
├── .env
├── .env.example
├── .eslintrc.json
├── biome.jsonc
├── components.json
├── drizzle.config.ts
├── instrumentation.ts
├── LICENSE
├── middleware.ts
├── next-env.d.ts
├── next.config.ts
├── package.json
├── PathRAG.log
├── playwright.config.ts
├── pnpm-lock.yaml
├── postcss.config.mjs
├── README.md
├── start_sh.sh
├── tailwind.config.ts
├── test-iframe.html
├── test-pathrag-integration.js
├── tsconfig.json
└── tsconfig.tsbuildinfo
```

The Chat SDK follows a standard Next.js App Router structure, with key logic organized into specific directories.

## Key Directories

- **`app/`**: Contains all routes, layouts, and UI components for the application.
    - `app/layout.tsx`: The root layout, often including global providers.
    - `app/(auth)/`: Route group for authentication-related pages and APIs.
        - `app/(auth)/auth.ts`: NextAuth configuration.
        - `app/(auth)/api/auth/`: Authentication API endpoints.
    - `app/(chat)/`: Route group for the main chat interface.
        - `app/(chat)/page.tsx`: The primary chat UI.
        - `app/(chat)/actions.ts`: Server actions for chat functionalities.
        - `app/(chat)/api/chat/route.ts`: Main chat API endpoint.
        - `app/(chat)/chat/[id]/page.tsx`: Page for individual chat conversations.

- **`lib/`**: Houses core business logic, utilities, and integrations.
    - `lib/ai/`: Core AI system logic.
        - `lib/ai/providers.ts`: Configuration for AI models and providers.
        - `lib/ai/models.ts`: Definitions for AI models.
        - `lib/ai/prompts.ts`: System prompts for the AI.
        - `lib/ai/middleware/`: Middleware for AI operations (e.g., caching, performance).
        - `lib/ai/tools/`: Definitions for AI function tools (e.g., knowledge base access).
    - `lib/db/`: Database layer.
        - `lib/db/schema.ts`: Drizzle ORM schema definitions.
        - `lib/db/queries.ts`: Database query operations.
    - `lib/types.ts`: Global TypeScript type definitions for the application.

For a more detailed overview of system patterns and architecture, refer to [.memory-bank/systemPatterns.md](mdc:.memory-bank/systemPatterns.md).

# AI System and Providers

The Chat SDK features a robust AI integration layer, designed for flexibility and performance.

## Core Components

- **AI SDK by Vercel:** ([package.json](mdc:package.json)) The primary library for interacting with various Large Language Models (LLMs). Version 4.3.13 is currently in use.
- **AI Model Providers:**
    - **Google Gemini:** The primary AI provider. Key models include:
        - `gemini-2.5-flash-preview-04-17` (for chat and reasoning)
        - `gemini-2.0-flash-lite` (for title generation)
    - **xAI Grok:** Supported as an alternative, with `grok-2-1212` documented as a default.
    - **OpenRouter:** Available for further provider options.
- **Google Gen AI SDK:** ([@google/genai](mdc:package.json) v1.1.0) Used for specific integrations, particularly with Google's caching system.
- **Provider Abstraction:** A key design pattern is the abstraction layer for AI providers, ensuring a consistent interface across different models. This is typically configured in `lib/ai/providers.ts`.
    ```typescript
    // Example of provider abstraction from systemPatterns.md
    const myProvider = customProvider({
      languageModels: {
        'chat-model': wrapLanguageModel({
          model: google('gemini-2.5-flash-preview-04-17'),
          middleware: [defaultSettingsMiddleware, cachingMiddleware]
        })
      }
    });
    ```
- **AI Middleware:** Custom middleware functions are used for tasks like performance tracking, request/response logging, and cache management. These are often found in `lib/ai/middleware/`.
- **AI Function Tools:** The system supports AI function calling/tools, defined in `lib/ai/tools/`. These allow the AI to interact with external systems or perform specific tasks (e.g., `getPathRAGKnowledge`).

For more details on the AI technology stack and specific configurations, see [.memory-bank/techContext.md](mdc:.memory-bank/techContext.md). For architectural patterns related to AI, refer to [.memory-bank/systemPatterns.md](mdc:.memory-bank/systemPatterns.md).

# Database and Schema

The Chat SDK utilizes PostgreSQL for its database needs, managed via Vercel Postgres and accessed through the Drizzle ORM for type-safe operations.

## Key Components

- **Database Provider:** PostgreSQL, hosted on [Vercel Postgres](httpsc:https://vercel.com/docs/storage/vercel-postgres).
- **ORM:** [Drizzle ORM](mdc:drizzle.config.ts) (version 0.34.0) is used for database interactions, providing type safety and a migration system ([Drizzle Kit](mdc:package.json)). The main Drizzle configuration can be found in `drizzle.config.ts`.
- **Schema Definitions:** Database table structures are defined in `lib/db/schema.ts`. This file contains the Drizzle schema definitions for all tables, such as `users`, `chats`, and `messages`.
- **Query Operations:** Pre-defined database queries and operations are typically located in `lib/db/queries.ts` or co-located with their respective features.

## Schema Version

- **Current Schema (V2):** The application is currently using **Schema V2**, which features a parts-based message system. This allows for more flexible message structures, including multimodal content.
- **Schema V1 (Deprecated):** An older, content-based message schema (Schema V1) is deprecated and scheduled for cleanup.

## Design Patterns

- **Type-Safe Database Operations:** Drizzle ORM enables fully type-safe queries. Prepared statements are often used for performance optimization.
    ```typescript
    // Example of a prepared statement from systemPatterns.md
    const getChatWithMessages = db
      .select()
      .from(chat)
      .leftJoin(message, eq(message.chatId, chat.id))
      .where(eq(chat.id, placeholder('chatId')))
      .prepare();
    ```
- **UUID Primary Keys:** Tables generally use UUIDs as primary keys, which is beneficial for distributed systems.
- **JSON Columns:** Flexible data storage is achieved using JSON columns where appropriate.

For more details on the database technology and schema design, consult [.memory-bank/techContext.md](mdc:.memory-bank/techContext.md) and [.memory-bank/systemPatterns.md](mdc:.memory-bank/systemPatterns.md).

# Authentication Flow

The Chat SDK implements a flexible authentication system using NextAuth.js, supporting both guest users and registered accounts.

## Core Components & Features

- **NextAuth.js:** ([app/(auth)/auth.ts](mdc:app/(auth)/auth.ts)) The project uses NextAuth 5.0 Beta for its authentication framework. The main configuration is typically found in `app/(auth)/auth.ts` or a similar file within the `app/(auth)/` directory group.
- **Guest Sessions:** Users can start interacting with the chatbot immediately without needing to register. A temporary guest session is automatically created.
- **User Authentication:** Standard email/password or OAuth-based authentication can be integrated for registered users, allowing for persistent chat history and user-specific settings.
- **Middleware Protection:** ([middleware.ts](mdc:middleware.ts) or `app/(auth)/auth.config.ts`) Next.js middleware is used for route-level access control, protecting certain routes or features that require authentication. The specific configuration can be found in `middleware.ts` or an auth-specific config like `app/(auth)/auth.config.ts`.
- **JWT Tokens:** Secure session management is handled using JSON Web Tokens (JWTs).
- **Type-Safe Sessions:** NextAuth types are often extended to ensure type safety when accessing session data throughout the application.

## Authentication Routes

Authentication-related pages (login, register) and API endpoints are typically grouped under `app/(auth)/`:
- `app/(auth)/login/`: Login page.
- `app/(auth)/register/`: Registration page.
- `app/(auth)/api/auth/[...nextauth]/route.ts`: The main NextAuth API handler.
- `app/(auth)/api/auth/guest/route.ts`: API endpoint possibly related to guest session creation or management.

For more details on the authentication patterns and technical setup, refer to [.memory-bank/systemPatterns.md](mdc:.memory-bank/systemPatterns.md) and [.memory-bank/techContext.md](mdc:.memory-bank/techContext.md).

# PathRAG Knowledge Base Integration

The Chat SDK integrates with PathRAG (Path Retrieval Augmented Generation) to provide access to a specialized knowledge base, enabling the chatbot to answer domain-specific questions.

## Key Components & Files

- **PathRAG API Server:** This is an external Python-based server that handles document retrieval and context generation.
    - `pathrag/pathrag_api.py`: The main API script for the PathRAG server. It handles requests and interacts with the document retriever.
    - `pathrag/document_retriever.py`: Contains the logic for loading, indexing, and searching documents from the knowledge base.
    - `pathrag/knowledgebase/`: This directory stores the actual documents that form the knowledge base (e.g., text files, markdown, JSON).
    - `pathrag/requirements.txt`: Lists Python dependencies for the PathRAG server.

- **AI Tool for PathRAG:** Within the Chat SDK, an AI tool is defined to allow the LLM to query the PathRAG API.
    - `lib/ai/tools/pathrag-knowledge.ts`: This TypeScript file defines the `getPathRAGKnowledge` tool. It handles making requests to the PathRAG API and processing its responses. It has been enhanced with robust error handling and logging.

- **API Endpoint Configuration:**
    - The Chat SDK is configured to connect to the PathRAG API at `http://localhost:8123/api/context`. This is set via the `PATHRAG_API_URL` environment variable (see [.env.example](mdc:.env.example)).

## Workflow

1. The LLM, when needing information from the knowledge base, uses the `getPathRAGKnowledge` tool.
2. The tool (`pathrag-knowledge.ts`) sends a request to the PathRAG API server (`pathrag_api.py`).
3. The PathRAG server (`document_retriever.py`) searches its `knowledgebase/` for relevant documents.
4. The server returns the retrieved context to the Chat SDK.
5. The LLM uses this context to formulate an answer.

## Testing

- An integration test script, `test-pathrag-integration.js`, is available to verify the connection and functionality between the Chat SDK and the PathRAG server.

For the latest status and details on the PathRAG integration, including recent fixes and enhancements, refer to [.memory-bank/activeContext.md](mdc:.memory-bank/activeContext.md) and [.memory-bank/progress.md](mdc:.memory-bank/progress.md).

---
*PathRAG integration enhanced - better error handling, debugging capability, và production readiness achieved.*
