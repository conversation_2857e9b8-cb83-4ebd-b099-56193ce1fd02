<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Embed Chat SDK</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .iframe-container {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
        }
        .info {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Chat SDK Embed Test</h1>
        
        <div class="info">
            <h3>Test Information:</h3>
            <ul>
                <li><strong>URL:</strong> https://8545-2405-4802-4f2-3710-5b6-e9e9-18e1-378e.ngrok-free.app/embed</li>
                <li><strong>User ID:</strong> test-user-123</li>
                <li><strong>Source:</strong> iframe-tester</li>
            </ul>
        </div>

        <div class="status success">
            ✅ Embed API hoạt động - test POST request thành công!
        </div>

        <div class="iframe-container">
            <h3>🖼️ Embedded Chat Interface:</h3>
            <iframe 
                src="https://8545-2405-4802-4f2-3710-5b6-e9e9-18e1-378e.ngrok-free.app/embed?user_id=test-user-123&source=iframe-tester"
                title="Chat SDK Embed"
                allow="microphone; camera"
                loading="lazy">
            </iframe>
        </div>

        <div class="info">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li>Giao diện chatbot sẽ xuất hiện trong iframe phía trên</li>
                <li>Thử nhắn tin "Xin chào" để test API</li>
                <li>Kiểm tra xem AI có phản hồi bằng tiếng Việt không</li>
                <li>Test tính năng PathRAG bằng cách hỏi về EMS</li>
            </ol>
        </div>
    </div>

    <script>
        // Listen for iframe communication
        window.addEventListener('message', function(event) {
            console.log('Received message from iframe:', event.data);
        });

        // Test iframe loading
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ Iframe loaded successfully');
        };
        iframe.onerror = function() {
            console.error('❌ Iframe failed to load');
        };
    </script>
</body>
</html>
