import { jwtVerify, SignJWT } from 'jose';
import { ExternalUser } from '../types/iframe';

// Secret key for JWT signing and verification
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'default-jwt-secret-for-iframe-authentication'
);

// JWT token expiration time (24 hours by default)
const TOKEN_EXPIRATION = '24h';

/**
 * Validates an iframe token and returns whether it's valid
 * @param token The JWT token to validate
 * @returns boolean indicating if the token is valid
 */
export async function validateIframeToken(token: string): Promise<boolean> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    return !!payload;
  } catch (error) {
    console.error('Error validating iframe token:', error);
    return false;
  }
}

/**
 * Checks if a token is in JWT format (contains dots)
 * @param token The token to check
 * @returns boolean indicating if the token is JWT format
 */
function isJWTFormat(token: string): boolean {
  return token.split('.').length === 3;
}

/**
 * Checks if a token is in UUID format
 * @param token The token to check
 * @returns boolean indicating if the token is UUID format
 */
function isUUIDFormat(token: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(token);
}

/**
 * Extracts user data from a JWT token or handles UUID tokens
 * @param token The JWT token containing user data or UUID token
 * @returns The external user data or null if invalid
 */
export function extractUserDataFromToken(token: string): ExternalUser | null {
  try {
    // Check if token is JWT format
    if (isJWTFormat(token)) {
      console.log('🎫 Processing JWT token');
      // For synchronous extraction, we use a simpler approach
      // This assumes the token is already validated elsewhere if needed
      const payload = JSON.parse(
        Buffer.from(token.split('.')[1], 'base64').toString()
      );
      
      if (!payload.externalId || !payload.source) {
        console.error('Invalid JWT token payload - missing required fields');
        return null;
      }
      
      return {
        externalId: payload.externalId,
        source: payload.source,
        metadata: payload.metadata || {}
      };
    }
    
    // Check if token is UUID format
    if (isUUIDFormat(token)) {
      console.log('🏷️ Processing UUID token, will require additional params');
      // UUID tokens need to be validated differently
      // For now, return null to trigger fallback authentication
      return null;
    }
    
    console.error('❌ Token is neither JWT nor UUID format');
    return null;
  } catch (error) {
    console.error('Error extracting user data from token:', error);
    return null;
  }
}

/**
 * Creates a JWT token for an external user
 * @param userData The external user data to encode in the token
 * @returns The generated JWT token
 */
export async function createIframeToken(userData: ExternalUser): Promise<string> {
  try {
    const token = await new SignJWT({
      externalId: userData.externalId,
      source: userData.source,
      metadata: userData.metadata || {}
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(TOKEN_EXPIRATION)
      .sign(JWT_SECRET);
    
    return token;
  } catch (error) {
    console.error('Error creating iframe token:', error);
    throw new Error('Failed to create authentication token');
  }
}