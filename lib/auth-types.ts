import type { DefaultSession } from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: DefaultSession['user'] & {
      id: string;
      type?: 'external' | 'authenticated';
      externalId?: string;
      source?: string;
      token?: string;
      tenant?: string;
      userName?: string;
    };
  }

  interface User {
    id: string;
    type?: 'external' | 'authenticated';
    externalId?: string;
    source?: string;
    token?: string;
    tenant?: string;
    userName?: string;
  }
}
